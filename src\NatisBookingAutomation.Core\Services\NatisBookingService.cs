using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using NatisBookingAutomation.Core.Constants;
using NatisBookingAutomation.Core.Interfaces;
using NatisBookingAutomation.Core.Models;
using System.Text.RegularExpressions;

namespace NatisBookingAutomation.Core.Services;

public class NatisBookingService : INatisBookingService, IDisposable
{
    private readonly ILogger<NatisBookingService> logger;
    private readonly BookingConfiguration config;
    private IPlaywright? playwright;
    private IBrowser? browser;
    private IPage? page;
    private bool disposed = false;

    public NatisBookingService(ILogger<NatisBookingService> logger, BookingConfiguration config)
    {
        this.logger = logger;
        this.config = config;
    }

    public async Task<BookingResult> BookMotorcycleLicenseTestAsync(CancellationToken cancellationToken = default)
    {
        var result = new BookingResult();
        var attempt = 0;

        try
        {
            await InitializeBrowserAsync();

            while (attempt < config.MaxRetryAttempts && !result.Success)
            {
                attempt++;
                logger.LogInformation("Starting booking attempt {Attempt} of {MaxAttempts}", attempt, config.MaxRetryAttempts);
                result.Logs.Add($"Attempt {attempt}: Starting booking process");

                try
                {
                    // Navigate to NATIS portal
                    await NavigateToPortalAsync();
                    result.Logs.Add("Navigated to NATIS portal");

                    // Handle authentication
                    if (!await IsLoggedInAsync())
                    {
                        logger.LogInformation("User not logged in, attempting login");
                        if (!await LoginAsync())
                        {
                            result.Message = "Failed to login";
                            result.Logs.Add("Login failed");
                            continue;
                        }
                        result.Logs.Add("Successfully logged in");
                    }
                    else
                    {
                        result.Logs.Add("Already logged in");
                    }

                    // Navigate to booking section
                    await NavigateToBookingSectionAsync();
                    result.Logs.Add("Navigated to booking section");

                    // Select province
                    await SelectProvinceAsync();
                    result.Logs.Add($"Selected province: {config.Province}");

                    // Configure test category
                    await ConfigureTestCategoryAsync();
                    result.Logs.Add("Configured test category and licence type");

                    // Get available centres
                    var availableCentres = await GetAvailableCentresAsync();
                    result.Logs.Add($"Found {availableCentres.Count} centres with availability");

                    // Find preferred centre with slots
                    var selectedCentre = availableCentres
                        .Where(c => c.IsPreferred && c.AvailableSlots > 0)
                        .OrderByDescending(c => c.AvailableSlots)
                        .FirstOrDefault();

                    if (selectedCentre == null)
                    {
                        logger.LogWarning("No preferred centres have available slots. Retrying in {Delay} seconds", config.RetryDelaySeconds);
                        result.Logs.Add("No available slots at preferred centres");

                        // Click cancel to go back to dashboard
                        await ClickCancelAsync();

                        if (attempt < config.MaxRetryAttempts)
                        {
                            await Task.Delay(TimeSpan.FromSeconds(config.RetryDelaySeconds), cancellationToken);
                        }
                        continue;
                    }

                    // Select centre and book
                    await SelectCentreAsync(selectedCentre.Name);
                    result.Logs.Add($"Selected centre: {selectedCentre.Name}");

                    await SelectEarliestDateAndTimeAsync();
                    result.Logs.Add("Selected earliest available date and time");

                    await CompleteBookingAsync();
                    result.Logs.Add("Completed booking");

                    // Success!
                    result.Success = true;
                    result.SelectedCentre = selectedCentre.Name;
                    result.Message = "Booking completed successfully";
                    result.AttemptsUsed = attempt;

                    // Navigate to success video
                    await NavigateToSuccessVideoAsync();
                    result.Logs.Add("Navigated to success video");

                    logger.LogInformation("Booking completed successfully at {Centre} after {Attempts} attempts",
                        selectedCentre.Name, attempt);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error during booking attempt {Attempt}", attempt);
                    result.Logs.Add($"Attempt {attempt} failed: {ex.Message}");

                    // Take screenshot on error
                    await TakeScreenshotAsync($"error_attempt_{attempt}");

                    if (attempt < config.MaxRetryAttempts)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(config.RetryDelaySeconds), cancellationToken);
                    }
                }
            }

            if (!result.Success)
            {
                result.Message = $"Failed to complete booking after {config.MaxRetryAttempts} attempts";
                result.AttemptsUsed = attempt;
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Fatal error during booking process");
            result.Exception = ex;
            result.Message = $"Fatal error: {ex.Message}";
            result.AttemptsUsed = attempt;
        }

        return result;
    }

    private async Task InitializeBrowserAsync()
    {
        if (playwright == null)
        {
            playwright = await Playwright.CreateAsync();

            var browserOptions = new BrowserTypeLaunchOptions
            {
                Headless = config.HeadlessMode,
                Timeout = config.PageTimeoutSeconds * 1000
            };

            // Try Edge first, fallback to Chromium
            //try
            //{
            //    var edgeOptions = new BrowserTypeLaunchOptions
            //    {
            //        Headless = config.HeadlessMode,
            //        Timeout = config.PageTimeoutSeconds * 1000,
            //        Channel = "msedge"
            //    };
            //    browser = await playwright.Chromium.LaunchAsync(edgeOptions);
            //    logger.LogInformation("Launched Microsoft Edge browser");
            //}
            //catch
            //{
            //    browser = await playwright.Chromium.LaunchAsync(browserOptions);
            //    logger.LogInformation("Launched Chromium browser (Edge not available)");
            //}

            browser = await playwright.Chromium.LaunchAsync(browserOptions);
            logger.LogInformation("Launched Chromium browser");

            page = await browser.NewPageAsync();
            page.SetDefaultTimeout(config.PageTimeoutSeconds * 1000);
        }
    }

    private async Task NavigateToPortalAsync()
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        logger.LogInformation("Navigating to NATIS portal: {Url}", NatisConstants.BaseUrl);
        await page.GotoAsync(NatisConstants.BaseUrl);
        await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
    }

    public async Task<bool> IsLoggedInAsync(CancellationToken cancellationToken = default)
    {
        if (page == null) return false;

        try
        {
            // Check if profile name is visible
            var profileElement = await page.QuerySelectorAsync($"text={config.ProfileName}");
            if (profileElement != null)
            {
                logger.LogInformation("User is already logged in as {ProfileName}", config.ProfileName);
                return true;
            }

            // Check if login/register links are visible
            var loginLink = await page.QuerySelectorAsync(NatisConstants.Selectors.LoginLink);
            var registerLink = await page.QuerySelectorAsync(NatisConstants.Selectors.RegisterLink);

            return loginLink == null && registerLink == null;
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Error checking login status");
            return false;
        }
    }

    public async Task<bool> LoginAsync(CancellationToken cancellationToken = default)
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        try
        {
            logger.LogInformation("Attempting to login with username: {Username}", config.Username);

            // Click login link if visible
            var loginLink = await page.QuerySelectorAsync(NatisConstants.Selectors.LoginLink);
            if (loginLink != null)
            {
                await loginLink.ClickAsync();
                await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            }

            // Handle redirect from identify page to login page
            if (page.Url.Contains("/auth/identify"))
            {
                logger.LogInformation("Redirecting from identify page to login page");
                await page.GotoAsync(NatisConstants.LoginUrl);
                await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            }

            // Fill login form
            await page.FillAsync("input[type='email'], input[name='username'], input[id='username']", config.Username);
            await page.FillAsync("input[type='password'], input[name='password'], input[id='password']", config.Password);

            // Click login button
            await page.ClickAsync("button[type='submit'], input[type='submit'], button:has-text('Login'), button:has-text('Sign In')");

            // Wait for navigation or login completion
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            await Task.Delay(NatisConstants.WaitTimes.Medium);

            // Verify login success
            return await IsLoggedInAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Login failed");
            return false;
        }
    }

    private async Task NavigateToBookingSectionAsync()
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        logger.LogInformation("Navigating to booking section");
        await page.ClickAsync(NatisConstants.Selectors.BookNowButton);
        await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
        await Task.Delay(NatisConstants.WaitTimes.Medium);
    }

    private async Task SelectProvinceAsync()
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        logger.LogInformation("Selecting province: {Province}", config.Province);

        // Try different selector patterns for province dropdown
        var provinceSelectors = new[]
        {
            NatisConstants.Selectors.ProvinceDropdown,
            "select[name='province']",
            "select:has(option:text('Gauteng'))",
            "[data-testid*='province']",
            "select"
        };

        foreach (var selector in provinceSelectors)
        {
            try
            {
                var element = await page.QuerySelectorAsync(selector);
                if (element != null)
                {
                    await element.SelectOptionAsync(new[] { config.Province });
                    break;
                }
            }
            catch (Exception ex)
            {
                logger.LogDebug(ex, "Failed to select province with selector: {Selector}", selector);
            }
        }

        // Click continue button
        await page.ClickAsync(NatisConstants.Selectors.ContinueButton);
        await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
        await Task.Delay(NatisConstants.WaitTimes.Medium);
    }

    private async Task ConfigureTestCategoryAsync()
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        logger.LogInformation("Configuring test category: {TestCategory}", config.TestCategory);

        // Set test category
        var testCategorySelectors = new[]
        {
            NatisConstants.Selectors.TestCategoryDropdown,
            "select[name*='category']",
            "select:has(option:text('MOTOR CYCLE'))",
            "[data-testid*='category']"
        };

        foreach (var selector in testCategorySelectors)
        {
            try
            {
                var element = await page.QuerySelectorAsync(selector);
                if (element != null)
                {
                    await element.SelectOptionAsync(new[] { config.TestCategory });
                    break;
                }
            }
            catch (Exception ex)
            {
                logger.LogDebug(ex, "Failed to select test category with selector: {Selector}", selector);
            }
        }

        await Task.Delay(NatisConstants.WaitTimes.Short);

        // Set licence test type
        logger.LogInformation("Configuring licence test type: {LicenceTestType}", config.LicenceTestType);

        var licenceTypeSelectors = new[]
        {
            NatisConstants.Selectors.LicenceTestTypeDropdown,
            "select[name*='type']",
            "select:has(option:text('EXCEEDING 125'))",
            "[data-testid*='type']"
        };

        foreach (var selector in licenceTypeSelectors)
        {
            try
            {
                var element = await page.QuerySelectorAsync(selector);
                if (element != null)
                {
                    await element.SelectOptionAsync(new[] { config.LicenceTestType });
                    break;
                }
            }
            catch (Exception ex)
            {
                logger.LogDebug(ex, "Failed to select licence test type with selector: {Selector}", selector);
            }
        }

        // Wait for slots to populate
        await Task.Delay(NatisConstants.WaitTimes.Long);
    }

    public async Task<List<CentreAvailability>> GetAvailableCentresAsync(CancellationToken cancellationToken = default)
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        var centres = new List<CentreAvailability>();

        try
        {
            logger.LogInformation("Getting available centres");

            // Try different selector patterns for centre dropdown
            var centreSelectors = new[]
            {
                NatisConstants.Selectors.CentreDropdown,
                "select[name*='centre']",
                "select option",
                "[data-testid*='centre'] option"
            };

            IElementHandle? centreDropdown = null;
            foreach (var selector in centreSelectors)
            {
                centreDropdown = await page.QuerySelectorAsync(selector.Replace(" option", ""));
                if (centreDropdown != null) break;
            }

            if (centreDropdown == null)
            {
                logger.LogWarning("Could not find centre dropdown");
                return centres;
            }

            // Get all options from the dropdown
            var options = await page.QuerySelectorAllAsync("select option, [role='option']");

            foreach (var option in options)
            {
                var text = await option.TextContentAsync();
                if (string.IsNullOrWhiteSpace(text)) continue;

                // Parse centre name and available slots
                // Format: "CENTRE NAME (X slots available)"
                var match = Regex.Match(text, @"^(.+?)\s*\((\d+)\s+slots?\s+available\)$", RegexOptions.IgnoreCase);

                if (match.Success)
                {
                    var centreName = match.Groups[1].Value.Trim();
                    var slotsText = match.Groups[2].Value;

                    if (int.TryParse(slotsText, out var availableSlots))
                    {
                        var centre = new CentreAvailability
                        {
                            Name = centreName,
                            AvailableSlots = availableSlots,
                            IsPreferred = config.PreferredCentres.Contains(centreName)
                        };

                        centres.Add(centre);
                        logger.LogDebug("Found centre: {Centre} with {Slots} slots (Preferred: {IsPreferred})",
                            centreName, availableSlots, centre.IsPreferred);
                    }
                }
            }

            logger.LogInformation("Found {TotalCentres} centres, {PreferredWithSlots} preferred centres with slots",
                centres.Count, centres.Count(c => c.IsPreferred && c.AvailableSlots > 0));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting available centres");
        }

        return centres;
    }

    private async Task SelectCentreAsync(string centreName)
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        logger.LogInformation("Selecting centre: {Centre}", centreName);

        var centreSelectors = new[]
        {
            NatisConstants.Selectors.CentreDropdown,
            "select[name*='centre']",
            "select"
        };

        foreach (var selector in centreSelectors)
        {
            try
            {
                var element = await page.QuerySelectorAsync(selector);
                if (element != null)
                {
                    // Try to select by text content that contains the centre name
                    await element.SelectOptionAsync(new SelectOptionValue { Label = centreName });
                    break;
                }
            }
            catch (Exception ex)
            {
                logger.LogDebug(ex, "Failed to select centre with selector: {Selector}", selector);
            }
        }

        await Task.Delay(NatisConstants.WaitTimes.Medium);
    }

    private async Task SelectEarliestDateAndTimeAsync()
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        logger.LogInformation("Selecting earliest available date and time");

        try
        {
            // Select earliest date
            var dateSelectors = new[]
            {
                NatisConstants.Selectors.DatePicker,
                "input[type='date']",
                "[data-testid*='date']",
                "select[name*='date']"
            };

            foreach (var selector in dateSelectors)
            {
                try
                {
                    var dateElement = await page.QuerySelectorAsync(selector);
                    if (dateElement != null)
                    {
                        // If it's a select dropdown, select the first available option
                        if (await dateElement.GetAttributeAsync("tagName") == "SELECT")
                        {
                            var options = await page.QuerySelectorAllAsync($"{selector} option");
                            if (options.Count > 1) // Skip first option which is usually placeholder
                            {
                                var firstOption = options[1];
                                var value = await firstOption.GetAttributeAsync("value");
                                if (!string.IsNullOrEmpty(value))
                                {
                                    await dateElement.SelectOptionAsync(new[] { value });
                                }
                            }
                        }
                        break;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogDebug(ex, "Failed to select date with selector: {Selector}", selector);
                }
            }

            await Task.Delay(NatisConstants.WaitTimes.Short);

            // Select first available time slot
            var timeSelectors = new[]
            {
                NatisConstants.Selectors.TimeSlotDropdown,
                "select[name*='time']",
                "[data-testid*='time']"
            };

            foreach (var selector in timeSelectors)
            {
                try
                {
                    var timeElement = await page.QuerySelectorAsync(selector);
                    if (timeElement != null)
                    {
                        var options = await page.QuerySelectorAllAsync($"{selector} option");
                        if (options.Count > 1) // Skip first option which is usually placeholder
                        {
                            var firstOption = options[1];
                            var value = await firstOption.GetAttributeAsync("value");
                            if (!string.IsNullOrEmpty(value))
                            {
                                await timeElement.SelectOptionAsync(new[] { value });
                            }
                        }
                        break;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogDebug(ex, "Failed to select time with selector: {Selector}", selector);
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error selecting date and time");
            throw;
        }

        await Task.Delay(NatisConstants.WaitTimes.Medium);
    }

    private async Task CompleteBookingAsync()
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        logger.LogInformation("Completing booking");

        try
        {
            // Click the "BOOK ONLY" button
            var bookButtonSelectors = new[]
            {
                NatisConstants.Selectors.BookOnlyButton,
                "button:has-text('BOOK ONLY')",
                "button:has-text('Book Only')",
                "button:has-text('BOOK')",
                "input[type='submit'][value*='BOOK']"
            };

            foreach (var selector in bookButtonSelectors)
            {
                try
                {
                    var element = await page.QuerySelectorAsync(selector);
                    if (element != null)
                    {
                        await element.ClickAsync();
                        break;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogDebug(ex, "Failed to click book button with selector: {Selector}", selector);
                }
            }

            // Wait for booking confirmation
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            await Task.Delay(NatisConstants.WaitTimes.Long);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error completing booking");
            throw;
        }
    }

    private async Task ClickCancelAsync()
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        logger.LogInformation("Clicking cancel to return to dashboard");

        try
        {
            var cancelSelectors = new[]
            {
                NatisConstants.Selectors.CancelButton,
                "button:has-text('Cancel')",
                "button:has-text('CANCEL')",
                "a:has-text('Cancel')"
            };

            foreach (var selector in cancelSelectors)
            {
                try
                {
                    var element = await page.QuerySelectorAsync(selector);
                    if (element != null)
                    {
                        await element.ClickAsync();
                        break;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogDebug(ex, "Failed to click cancel with selector: {Selector}", selector);
                }
            }

            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            await Task.Delay(NatisConstants.WaitTimes.Medium);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Error clicking cancel button");
        }
    }

    private async Task NavigateToSuccessVideoAsync()
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        try
        {
            logger.LogInformation("Navigating to success video: {Url}", config.SuccessVideoUrl);
            await page.GotoAsync(config.SuccessVideoUrl);
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to navigate to success video");
        }
    }

    private async Task TakeScreenshotAsync(string filename)
    {
        if (page == null) return;

        try
        {
            var screenshotPath = Path.Combine("screenshots", $"{filename}_{DateTime.Now:yyyyMMdd_HHmmss}.png");
            Directory.CreateDirectory(Path.GetDirectoryName(screenshotPath)!);

            await page.ScreenshotAsync(new PageScreenshotOptions
            {
                Path = screenshotPath,
                FullPage = true
            });

            logger.LogInformation("Screenshot saved: {Path}", screenshotPath);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to take screenshot");
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!disposed && disposing)
        {
            try
            {
                browser?.DisposeAsync().AsTask().Wait(TimeSpan.FromSeconds(5));
                playwright?.Dispose();
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "Error disposing browser resources");
            }

            disposed = true;
        }
    }
}
