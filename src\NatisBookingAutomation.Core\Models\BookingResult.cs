namespace NatisBookingAutomation.Core.Models;

public class BookingResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? SelectedCentre { get; set; }
    public DateTime? BookingDate { get; set; }
    public string? BookingTime { get; set; }
    public int AttemptsUsed { get; set; }
    public Exception? Exception { get; set; }
    public List<string> Logs { get; set; } = new();
}

public class CentreAvailability
{
    public string Name { get; set; } = string.Empty;
    public int AvailableSlots { get; set; }
    public bool IsPreferred { get; set; }
}
