namespace NatisBookingAutomation.Core.Models;

public class BookingConfiguration
{
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string ProfileName { get; set; } = "GERT DU PLESSIS";
    public string Province { get; set; } = "Gauteng";
    public string TestCategory { get; set; } = "03 - DRIVING LICENSE FOR MOTOR CYCLE";
    public string LicenceTestType { get; set; } = "A - MOTOR CYCLE, EXCEEDING 125 CM3";
    public List<string> PreferredCentres { get; set; } = new()
    {
        "Roodepoort DLTC",
        "Krugersdorp DLTC",
        "Randfontein DLTC",
        "Westonaria DLTC",
        "Langlaagte DLTC",
        "Randburg DLTC",
        "Sandton DLTC"
    };
    public int MaxRetryAttempts { get; set; } = 10;
    public int RetryDelaySeconds { get; set; } = 30;
    public int PageTimeoutSeconds { get; set; } = 30;
    public bool HeadlessMode { get; set; } = false;
    public string SuccessVideoUrl { get; set; } = "https://www.youtube.com/shorts/SXHMnicI6Pg";
}
